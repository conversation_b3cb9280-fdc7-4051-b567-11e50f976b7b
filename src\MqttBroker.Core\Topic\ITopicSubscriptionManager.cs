using MqttBroker.Core.Client;
using MqttBroker.Core.Protocol;
using MqttBroker.Core.Protocol.Packets;

namespace MqttBroker.Core.Topic;

/// <summary>
/// 主题订阅管理器接口
/// </summary>
public interface ITopicSubscriptionManager
{
    /// <summary>
    /// 订阅主题
    /// </summary>
    /// <param name="client">客户端</param>
    /// <param name="subscription">订阅信息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅结果</returns>
    Task<SubscriptionResult> SubscribeAsync(IMqttClient client, MqttSubscription subscription, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量订阅主题
    /// </summary>
    /// <param name="client">客户端</param>
    /// <param name="subscriptions">订阅信息列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>订阅结果列表</returns>
    Task<IList<SubscriptionResult>> SubscribeAsync(IMqttClient client, IList<MqttSubscription> subscriptions, CancellationToken cancellationToken = default);

    /// <summary>
    /// 取消订阅主题
    /// </summary>
    /// <param name="client">客户端</param>
    /// <param name="topicFilter">主题过滤器</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>取消订阅结果</returns>
    Task<UnsubscriptionResult> UnsubscribeAsync(IMqttClient client, string topicFilter, CancellationToken cancellationToken = default);

    /// <summary>
    /// 批量取消订阅主题
    /// </summary>
    /// <param name="client">客户端</param>
    /// <param name="topicFilters">主题过滤器列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>取消订阅结果列表</returns>
    Task<IList<UnsubscriptionResult>> UnsubscribeAsync(IMqttClient client, IList<string> topicFilters, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取匹配指定主题的所有订阅者
    /// </summary>
    /// <param name="topicName">主题名称</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>匹配的订阅者列表</returns>
    Task<IList<TopicSubscriber>> GetSubscribersAsync(string topicName, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取客户端的所有订阅
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>客户端订阅列表</returns>
    Task<IList<ClientSubscription>> GetClientSubscriptionsAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 清理客户端的所有订阅
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理任务</returns>
    Task CleanupClientSubscriptionsAsync(string clientId, CancellationToken cancellationToken = default);

    /// <summary>
    /// 获取订阅统计信息
    /// </summary>
    /// <returns>订阅统计信息</returns>
    Task<SubscriptionStatistics> GetStatisticsAsync();

    /// <summary>
    /// 订阅者添加事件
    /// </summary>
    event EventHandler<SubscriberAddedEventArgs>? SubscriberAdded;

    /// <summary>
    /// 订阅者移除事件
    /// </summary>
    event EventHandler<SubscriberRemovedEventArgs>? SubscriberRemoved;
}

/// <summary>
/// 订阅结果
/// </summary>
public class SubscriptionResult
{
    /// <summary>
    /// 主题过滤器
    /// </summary>
    public string TopicFilter { get; set; } = string.Empty;

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 授权的QoS级别
    /// </summary>
    public MqttQoSLevel GrantedQoSLevel { get; set; }

    /// <summary>
    /// 原因码
    /// </summary>
    public MqttReasonCode ReasonCode { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 创建成功的订阅结果
    /// </summary>
    /// <param name="topicFilter">主题过滤器</param>
    /// <param name="grantedQoSLevel">授权的QoS级别</param>
    /// <returns>订阅结果</returns>
    public static SubscriptionResult Success(string topicFilter, MqttQoSLevel grantedQoSLevel)
    {
        return new SubscriptionResult
        {
            TopicFilter = topicFilter,
            IsSuccess = true,
            GrantedQoSLevel = grantedQoSLevel,
            ReasonCode = grantedQoSLevel switch
            {
                MqttQoSLevel.AtMostOnce => MqttReasonCode.GrantedQoS0,
                MqttQoSLevel.AtLeastOnce => MqttReasonCode.GrantedQoS1,
                MqttQoSLevel.ExactlyOnce => MqttReasonCode.GrantedQoS2,
                _ => MqttReasonCode.UnspecifiedError
            }
        };
    }

    /// <summary>
    /// 创建失败的订阅结果
    /// </summary>
    /// <param name="topicFilter">主题过滤器</param>
    /// <param name="reasonCode">原因码</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>订阅结果</returns>
    public static SubscriptionResult Failure(string topicFilter, MqttReasonCode reasonCode, string? errorMessage = null)
    {
        return new SubscriptionResult
        {
            TopicFilter = topicFilter,
            IsSuccess = false,
            ReasonCode = reasonCode,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// 取消订阅结果
/// </summary>
public class UnsubscriptionResult
{
    /// <summary>
    /// 主题过滤器
    /// </summary>
    public string TopicFilter { get; set; } = string.Empty;

    /// <summary>
    /// 是否成功
    /// </summary>
    public bool IsSuccess { get; set; }

    /// <summary>
    /// 原因码
    /// </summary>
    public MqttReasonCode ReasonCode { get; set; }

    /// <summary>
    /// 错误消息
    /// </summary>
    public string? ErrorMessage { get; set; }

    /// <summary>
    /// 创建成功的取消订阅结果
    /// </summary>
    /// <param name="topicFilter">主题过滤器</param>
    /// <returns>取消订阅结果</returns>
    public static UnsubscriptionResult Success(string topicFilter)
    {
        return new UnsubscriptionResult
        {
            TopicFilter = topicFilter,
            IsSuccess = true,
            ReasonCode = MqttReasonCode.Success
        };
    }

    /// <summary>
    /// 创建失败的取消订阅结果
    /// </summary>
    /// <param name="topicFilter">主题过滤器</param>
    /// <param name="reasonCode">原因码</param>
    /// <param name="errorMessage">错误消息</param>
    /// <returns>取消订阅结果</returns>
    public static UnsubscriptionResult Failure(string topicFilter, MqttReasonCode reasonCode, string? errorMessage = null)
    {
        return new UnsubscriptionResult
        {
            TopicFilter = topicFilter,
            IsSuccess = false,
            ReasonCode = reasonCode,
            ErrorMessage = errorMessage
        };
    }
}

/// <summary>
/// 主题订阅者
/// </summary>
public class TopicSubscriber
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 主题过滤器
    /// </summary>
    public string TopicFilter { get; set; } = string.Empty;

    /// <summary>
    /// QoS级别
    /// </summary>
    public MqttQoSLevel QoSLevel { get; set; }

    /// <summary>
    /// 订阅选项
    /// </summary>
    public MqttSubscriptionOptions Options { get; set; } = new();

    /// <summary>
    /// 订阅时间
    /// </summary>
    public DateTime SubscribedAt { get; set; }

    /// <summary>
    /// 客户端引用
    /// </summary>
    public IMqttClient? Client { get; set; }
}

/// <summary>
/// 客户端订阅信息
/// </summary>
public class ClientSubscription
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; set; } = string.Empty;

    /// <summary>
    /// 主题过滤器
    /// </summary>
    public string TopicFilter { get; set; } = string.Empty;

    /// <summary>
    /// QoS级别
    /// </summary>
    public MqttQoSLevel QoSLevel { get; set; }

    /// <summary>
    /// 订阅选项
    /// </summary>
    public MqttSubscriptionOptions Options { get; set; } = new();

    /// <summary>
    /// 订阅时间
    /// </summary>
    public DateTime SubscribedAt { get; set; }
}

/// <summary>
/// 订阅统计信息
/// </summary>
public class SubscriptionStatistics
{
    /// <summary>
    /// 总订阅数
    /// </summary>
    public long TotalSubscriptions { get; set; }

    /// <summary>
    /// 活跃客户端数
    /// </summary>
    public long ActiveClients { get; set; }

    /// <summary>
    /// 主题数量
    /// </summary>
    public long TopicCount { get; set; }

    /// <summary>
    /// 通配符订阅数
    /// </summary>
    public long WildcardSubscriptions { get; set; }

    /// <summary>
    /// 系统主题订阅数
    /// </summary>
    public long SystemTopicSubscriptions { get; set; }

    /// <summary>
    /// 平均每个客户端的订阅数
    /// </summary>
    public double AverageSubscriptionsPerClient => ActiveClients > 0 ? (double)TotalSubscriptions / ActiveClients : 0;
}

/// <summary>
/// 订阅者添加事件参数
/// </summary>
public class SubscriberAddedEventArgs : EventArgs
{
    /// <summary>
    /// 订阅者
    /// </summary>
    public TopicSubscriber Subscriber { get; }

    /// <summary>
    /// 初始化订阅者添加事件参数
    /// </summary>
    /// <param name="subscriber">订阅者</param>
    public SubscriberAddedEventArgs(TopicSubscriber subscriber)
    {
        Subscriber = subscriber ?? throw new ArgumentNullException(nameof(subscriber));
    }
}

/// <summary>
/// 订阅者移除事件参数
/// </summary>
public class SubscriberRemovedEventArgs : EventArgs
{
    /// <summary>
    /// 客户端ID
    /// </summary>
    public string ClientId { get; }

    /// <summary>
    /// 主题过滤器
    /// </summary>
    public string TopicFilter { get; }

    /// <summary>
    /// 初始化订阅者移除事件参数
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="topicFilter">主题过滤器</param>
    public SubscriberRemovedEventArgs(string clientId, string topicFilter)
    {
        ClientId = clientId ?? throw new ArgumentNullException(nameof(clientId));
        TopicFilter = topicFilter ?? throw new ArgumentNullException(nameof(topicFilter));
    }
}
