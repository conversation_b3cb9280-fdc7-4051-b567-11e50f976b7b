using System.Collections.Concurrent;
using Microsoft.Extensions.Logging;

namespace MqttBroker.Core.Message;

/// <summary>
/// 内存死信队列服务实现
/// </summary>
public class InMemoryDeadLetterQueueService : IDeadLetterQueueService
{
    private readonly ILogger<InMemoryDeadLetterQueueService> _logger;
    private readonly IMessageRoutingEngine _routingEngine;
    private readonly ConcurrentDictionary<string, DeadLetterMessage> _deadLetterMessages;
    private readonly ConcurrentDictionary<string, ConcurrentQueue<DeadLetterMessage>> _clientDeadLetterMessages;
    private readonly object _statsLock = new();
    private long _totalDeadLetterMessages;
    private long _reprocessedSuccessCount;
    private long _reprocessedFailedCount;

    /// <summary>
    /// 初始化内存死信队列服务
    /// </summary>
    /// <param name="logger">日志记录器</param>
    /// <param name="routingEngine">消息路由引擎</param>
    public InMemoryDeadLetterQueueService(
        ILogger<InMemoryDeadLetterQueueService> logger,
        IMessageRoutingEngine routingEngine)
    {
        _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        _routingEngine = routingEngine ?? throw new ArgumentNullException(nameof(routingEngine));
        _deadLetterMessages = new ConcurrentDictionary<string, DeadLetterMessage>();
        _clientDeadLetterMessages = new ConcurrentDictionary<string, ConcurrentQueue<DeadLetterMessage>>();
    }

    /// <summary>
    /// 添加消息到死信队列
    /// </summary>
    /// <param name="deadLetterMessage">死信消息</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功添加</returns>
    public Task<bool> AddToDeadLetterQueueAsync(DeadLetterMessage deadLetterMessage, CancellationToken cancellationToken = default)
    {
        if (deadLetterMessage == null)
            throw new ArgumentNullException(nameof(deadLetterMessage));

        try
        {
            // 添加到全局索引
            _deadLetterMessages[deadLetterMessage.MessageId] = deadLetterMessage;

            // 添加到客户端索引
            var clientQueue = _clientDeadLetterMessages.GetOrAdd(deadLetterMessage.ClientId, _ => new ConcurrentQueue<DeadLetterMessage>());
            clientQueue.Enqueue(deadLetterMessage);

            lock (_statsLock)
            {
                _totalDeadLetterMessages++;
            }

            _logger.LogWarning("Added message to dead letter queue: MessageId={MessageId}, ClientId={ClientId}, Topic={Topic}, Reason={Reason}", 
                deadLetterMessage.MessageId, deadLetterMessage.ClientId, deadLetterMessage.TopicName, deadLetterMessage.FailureReason);

            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error adding message to dead letter queue: {MessageId}", deadLetterMessage.MessageId);
            return Task.FromResult(false);
        }
    }

    /// <summary>
    /// 获取死信队列消息
    /// </summary>
    /// <param name="maxMessages">最大消息数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>死信消息列表</returns>
    public Task<IList<DeadLetterMessage>> GetDeadLetterMessagesAsync(int maxMessages = 100, CancellationToken cancellationToken = default)
    {
        try
        {
            var messages = _deadLetterMessages.Values
                .OrderBy(m => m.DeadLetterTime)
                .Take(maxMessages)
                .ToList();

            _logger.LogTrace("Retrieved {Count} dead letter messages", messages.Count);
            return Task.FromResult<IList<DeadLetterMessage>>(messages);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting dead letter messages");
            return Task.FromResult<IList<DeadLetterMessage>>(new List<DeadLetterMessage>());
        }
    }

    /// <summary>
    /// 根据客户端ID获取死信消息
    /// </summary>
    /// <param name="clientId">客户端ID</param>
    /// <param name="maxMessages">最大消息数量</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>死信消息列表</returns>
    public Task<IList<DeadLetterMessage>> GetDeadLetterMessagesByClientAsync(string clientId, int maxMessages = 100, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(clientId))
            return Task.FromResult<IList<DeadLetterMessage>>(new List<DeadLetterMessage>());

        try
        {
            if (!_clientDeadLetterMessages.TryGetValue(clientId, out var clientQueue))
                return Task.FromResult<IList<DeadLetterMessage>>(new List<DeadLetterMessage>());

            var messages = clientQueue.ToArray()
                .Take(maxMessages)
                .ToList();

            _logger.LogTrace("Retrieved {Count} dead letter messages for client: {ClientId}", messages.Count, clientId);
            return Task.FromResult<IList<DeadLetterMessage>>(messages);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting dead letter messages for client: {ClientId}", clientId);
            return Task.FromResult<IList<DeadLetterMessage>>(new List<DeadLetterMessage>());
        }
    }

    /// <summary>
    /// 重新处理死信消息
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>重新处理结果</returns>
    public async Task<DeadLetterReprocessResult> ReprocessDeadLetterMessageAsync(string messageId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(messageId))
            throw new ArgumentException("Message ID cannot be null or empty", nameof(messageId));

        var processedAt = DateTime.UtcNow;

        try
        {
            if (!_deadLetterMessages.TryGetValue(messageId, out var deadLetterMessage))
            {
                return new DeadLetterReprocessResult
                {
                    IsSuccess = false,
                    MessageId = messageId,
                    ErrorMessage = "Dead letter message not found",
                    ProcessedAt = processedAt
                };
            }

            _logger.LogInformation("Reprocessing dead letter message: {MessageId} for client: {ClientId}", 
                messageId, deadLetterMessage.ClientId);

            // 转换为发布数据包并重新路由
            var publishPacket = deadLetterMessage.ToPublishPacket();
            var routingResult = await _routingEngine.RouteMessageAsync(publishPacket, deadLetterMessage.ClientId, cancellationToken);

            if (routingResult.IsSuccess)
            {
                // 重新处理成功，从死信队列中移除
                await DeleteDeadLetterMessageAsync(messageId, cancellationToken);

                lock (_statsLock)
                {
                    _reprocessedSuccessCount++;
                }

                _logger.LogInformation("Successfully reprocessed dead letter message: {MessageId}", messageId);

                return new DeadLetterReprocessResult
                {
                    IsSuccess = true,
                    MessageId = messageId,
                    RoutingResult = routingResult,
                    ProcessedAt = processedAt
                };
            }
            else
            {
                // 重新处理失败，更新重试次数
                deadLetterMessage.RetryCount++;
                deadLetterMessage.LastRetryTime = processedAt;

                lock (_statsLock)
                {
                    _reprocessedFailedCount++;
                }

                _logger.LogWarning("Failed to reprocess dead letter message: {MessageId}, error: {Error}", 
                    messageId, routingResult.ErrorMessage);

                return new DeadLetterReprocessResult
                {
                    IsSuccess = false,
                    MessageId = messageId,
                    RoutingResult = routingResult,
                    ErrorMessage = routingResult.ErrorMessage,
                    ProcessedAt = processedAt
                };
            }
        }
        catch (Exception ex)
        {
            lock (_statsLock)
            {
                _reprocessedFailedCount++;
            }

            _logger.LogError(ex, "Error reprocessing dead letter message: {MessageId}", messageId);

            return new DeadLetterReprocessResult
            {
                IsSuccess = false,
                MessageId = messageId,
                ErrorMessage = ex.Message,
                ProcessedAt = processedAt
            };
        }
    }

    /// <summary>
    /// 批量重新处理死信消息
    /// </summary>
    /// <param name="messageIds">消息ID列表</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>批量重新处理结果</returns>
    public async Task<BatchReprocessResult> ReprocessDeadLetterMessagesAsync(IList<string> messageIds, CancellationToken cancellationToken = default)
    {
        if (messageIds == null || messageIds.Count == 0)
            return new BatchReprocessResult { TotalCount = 0, SuccessCount = 0, FailedCount = 0 };

        var startTime = DateTime.UtcNow;
        var result = new BatchReprocessResult
        {
            TotalCount = messageIds.Count,
            Results = new List<DeadLetterReprocessResult>()
        };

        foreach (var messageId in messageIds)
        {
            var reprocessResult = await ReprocessDeadLetterMessageAsync(messageId, cancellationToken);
            result.Results.Add(reprocessResult);

            if (reprocessResult.IsSuccess)
            {
                result.SuccessCount++;
            }
            else
            {
                result.FailedCount++;
            }
        }

        result.ElapsedMilliseconds = (long)(DateTime.UtcNow - startTime).TotalMilliseconds;

        _logger.LogInformation("Batch reprocessing completed: {SuccessCount}/{TotalCount} messages reprocessed successfully in {ElapsedMs}ms", 
            result.SuccessCount, result.TotalCount, result.ElapsedMilliseconds);

        return result;
    }

    /// <summary>
    /// 删除死信消息
    /// </summary>
    /// <param name="messageId">消息ID</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>是否成功删除</returns>
    public Task<bool> DeleteDeadLetterMessageAsync(string messageId, CancellationToken cancellationToken = default)
    {
        if (string.IsNullOrWhiteSpace(messageId))
            return Task.FromResult(false);

        try
        {
            if (!_deadLetterMessages.TryRemove(messageId, out var deadLetterMessage))
                return Task.FromResult(false);

            // 从客户端队列中移除（简化处理）
            // 注意：ConcurrentQueue不支持直接删除特定元素，实际实现中可能需要使用其他数据结构

            _logger.LogTrace("Deleted dead letter message: {MessageId} for client: {ClientId}", 
                messageId, deadLetterMessage.ClientId);

            return Task.FromResult(true);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error deleting dead letter message: {MessageId}", messageId);
            return Task.FromResult(false);
        }
    }

    /// <summary>
    /// 清理过期死信消息
    /// </summary>
    /// <param name="expiredBefore">过期时间点</param>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>清理结果</returns>
    public Task<DeadLetterCleanupResult> CleanupExpiredDeadLetterMessagesAsync(DateTime expiredBefore, CancellationToken cancellationToken = default)
    {
        var cleanedCount = 0;
        var startTime = DateTime.UtcNow;

        try
        {
            var expiredMessageIds = new List<string>();

            // 查找过期消息
            foreach (var kvp in _deadLetterMessages)
            {
                var message = kvp.Value;
                if (message.ExpiresAt.HasValue && message.ExpiresAt.Value < expiredBefore)
                {
                    expiredMessageIds.Add(message.MessageId);
                }
            }

            // 删除过期消息
            foreach (var messageId in expiredMessageIds)
            {
                if (_deadLetterMessages.TryRemove(messageId, out var message))
                {
                    cleanedCount++;
                    _logger.LogTrace("Cleaned up expired dead letter message {MessageId} for client: {ClientId}", 
                        messageId, message.ClientId);
                }
            }

            var elapsed = DateTime.UtcNow - startTime;

            _logger.LogInformation("Cleaned up {Count} expired dead letter messages in {ElapsedMs}ms", 
                cleanedCount, elapsed.TotalMilliseconds);

            return Task.FromResult(new DeadLetterCleanupResult
            {
                IsSuccess = true,
                CleanedCount = cleanedCount,
                ElapsedMilliseconds = (long)elapsed.TotalMilliseconds
            });
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error cleaning up expired dead letter messages");
            
            return Task.FromResult(new DeadLetterCleanupResult
            {
                IsSuccess = false,
                CleanedCount = cleanedCount,
                ErrorMessage = ex.Message
            });
        }
    }

    /// <summary>
    /// 获取死信队列统计信息
    /// </summary>
    /// <param name="cancellationToken">取消令牌</param>
    /// <returns>统计信息</returns>
    public Task<DeadLetterStatistics> GetStatisticsAsync(CancellationToken cancellationToken = default)
    {
        try
        {
            var stats = new DeadLetterStatistics();
            var today = DateTime.UtcNow.Date;

            lock (_statsLock)
            {
                stats.ReprocessedSuccessCount = _reprocessedSuccessCount;
                stats.ReprocessedFailedCount = _reprocessedFailedCount;
            }

            stats.TotalDeadLetterMessages = _deadLetterMessages.Count;
            stats.TodayDeadLetterMessages = _deadLetterMessages.Values.Count(m => m.DeadLetterTime.Date == today);

            if (_deadLetterMessages.Count > 0)
            {
                var allMessages = _deadLetterMessages.Values.ToArray();
                
                // 按失败原因分组统计
                stats.MessagesByReason = allMessages
                    .GroupBy(m => m.FailureType)
                    .ToDictionary(g => g.Key, g => (long)g.Count());

                // 按客户端分组统计
                stats.MessagesByClient = allMessages
                    .GroupBy(m => m.ClientId)
                    .ToDictionary(g => g.Key, g => (long)g.Count());

                // 计算平均消息大小
                stats.AverageMessageSize = allMessages.Average(m => m.Payload.Length);

                // 最早和最新消息时间
                stats.EarliestDeadLetterTime = allMessages.Min(m => m.DeadLetterTime);
                stats.LatestDeadLetterTime = allMessages.Max(m => m.DeadLetterTime);
            }

            return Task.FromResult(stats);
        }
        catch (Exception ex)
        {
            _logger.LogError(ex, "Error getting dead letter queue statistics");
            return Task.FromResult(new DeadLetterStatistics());
        }
    }
}
